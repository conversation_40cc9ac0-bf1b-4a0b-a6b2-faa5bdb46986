package com.olading.operate.labor.domain.proxy;

import cn.hutool.json.JSONUtil;
import com.lanmaoly.cloud.psalary.mybank.CardAttribute;
import com.lanmaoly.cloud.psalary.mybank.MybankAccountParam;
import com.lanmaoly.cloud.psalary.mybank.MybankAccountResult;
import com.lanmaoly.cloud.psalary.mybank.MybankQueryParam;
import com.lanmaoly.cloud.psalary.mybank.MybankQueryResult;
import com.lanmaoly.cloud.psalary.mybank.MybankRemitParam;
import com.lanmaoly.cloud.psalary.mybank.MybankRemitResult;
import com.lanmaoly.cloud.psalary.mybank.MybankRemitStatus;
import com.lanmaoly.cloud.psalary.mybank.RemitService;
import com.lanmaoly.cloud.psalary.mybank.ResultCode;
import com.lanmaoly.cloud.psalary.service.consts.SourceDomainEnum;
import com.lanmaoly.cloud.psalary.service.consts.TargetDomainEnum;
import com.olading.boot.core.business.BusinessException;
import com.olading.operate.labor.domain.corporation.CorporationPayChannelEntity;
import com.olading.operate.labor.domain.proxy.channel.ChannelRemitOrderEntity;
import com.olading.operate.labor.domain.proxy.channel.ChannelRemitOrderRepo;
import com.olading.operate.labor.domain.proxy.channel.ChannelRemitRequest;
import com.olading.operate.labor.domain.proxy.channel.RemitStatusEnum;
import com.olading.operate.labor.util.JSONUtils;
import com.olading.operate.labor.util.RedisLockType;
import com.olading.operate.labor.util.RedisUtils;
import com.olading.operate.labor.util.ThreadPoolUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
* @description: 
* @author: zhuangweifeng
* @time: 2025/7/9 14:22
*/
@RequiredArgsConstructor
@Component
@Slf4j
public class ChannelRemitProvider {
    private final RemitService remitService;
    private final ChannelRemitOrderRepo remitOrderManager;
    private final ProxyOrderManager proxyOrderManager;
    private final RedisUtils redisUtils;


    /**
     * 入网通道
     * @param param
     */
    public void createAccount(CorporationPayChannelEntity param) {

        if(StringUtils.isBlank(param.getChannelConfig()) || StringUtils.isBlank(param.getPayChannel()) || param.getSupplierCorporationId() == null){
            throw new BusinessException("通道配置信息不能为空");
        }

        //转换成参数
        final MybankAccountParam accountParam = JSONUtil.toBean(param.getChannelConfig(), MybankAccountParam.class);
        accountParam.setSourceDomainCode(SourceDomainEnum.LABOR.name());
        accountParam.setTargetDomainCode(TargetDomainEnum.valueOf(param.getPayChannel()).name());
        accountParam.setSourceId(param.getSupplierCorporationId().toString());
        try {
            switch (TargetDomainEnum.valueOf(param.getPayChannel())){
                case CMBCLOUD -> accountParam.setEncryType("SM4");
                case PABC -> accountParam.setRemoteUrl("https://api.pabclife.com/api/v1/");
                default -> throw new BusinessException("不支持的通道");
            }
            final MybankAccountResult mybankAccount = remitService.createMybankAccount(accountParam);
            if(mybankAccount.getResultCode() != ResultCode.T){
                log.info("支付通道开通变更失败,{}", mybankAccount.getErrorMessage());
                throw new BusinessException("支付通道开通变更失败,"+ mybankAccount.getErrorMessage());
            }
        } catch (Exception e) {
            log.error("支付通道开通变更失败", e);
            throw new BusinessException("支付通道开通变更失败");
        }
    }

    public void processRemit(ChannelRemitRequest param) {
        log.info("ChannelRemitProvider-出款模块开始，代付订单号:{}", param.getProxyOrderId());
        //生成提现订单
        ChannelRemitOrderEntity remitOrder =createRemitOrder(param);
        //异步调用di系统出款
        ThreadPoolUtil.executeTransactionThreadPool(() -> {
            doRemit(remitOrder);
        });

    }


    /**
     * 订单出款
     * @param param
     */
    private ChannelRemitOrderEntity createRemitOrder(ChannelRemitRequest param) {
        return remitOrderManager.createRemitOrder(param);
    }

    public static MybankRemitParam of(ChannelRemitOrderEntity entity){
        final MybankRemitParam mybankRemitParam = new MybankRemitParam();
        mybankRemitParam.setRequestNo(entity.getRequestNo());
        mybankRemitParam.setSourceId(entity.getSupplierCorporationId().toString());
        mybankRemitParam.setBankcardNo(entity.getBankCard());
        mybankRemitParam.setAccountName(entity.getName());
        mybankRemitParam.setBankName(entity.getBankName());
        mybankRemitParam.setTargetDomainCode(entity.getPayChannel());
        mybankRemitParam.setMobile(entity.getCellphone());
        mybankRemitParam.setSourceDomainCode(SourceDomainEnum.LABOR.name());
        mybankRemitParam.setCardAttribute(CardAttribute.C);
        mybankRemitParam.setUsedFor("CHANNEL_WITHDRAW");
        mybankRemitParam.setTaxModel("SPECIAL");
        mybankRemitParam.setMemo(entity.getRemark());
        mybankRemitParam.setAmount(entity.getAmount());
        return mybankRemitParam;
    }

    public void doRemit(ChannelRemitOrderEntity entity) {
        log.info("ChannelRemitProvider-调用出款结果:{}", JSONUtils.json(entity));
        final MybankRemitParam mybankRemitParam = of(entity);
        try {
            MybankRemitResult remit = remitService.remit(mybankRemitParam);
            log.info("ChannelRemitProvider-调用出款结果:{}", JSONUtils.json(remit));
            //结果处理
            processResult(remit,entity);
        } catch (Exception e) {
            log.info("调用出款通道,提现订单:{}-异常", JSONUtils.json(entity), e);
        }

    }

    private void processResult(MybankRemitResult remit, ChannelRemitOrderEntity order) {
        ResultCode resultCode = remit.getResultCode();
        //受理失败
        if (ResultCode.F == resultCode) {
            log.info("ChannelRemitProvider-processResult-出款系统受理失败，代付订单置 失败,id:{}", order.getId());
            proxyOrderManager.processRemitAcceptFail(order,remit.getErrorCode(), remit.getErrorMessage());
        }else{
            log.info("ChannelRemitProvider-processResult-出款系统受理成功，代付订单置 成功,id:{}", order.getId());
            proxyOrderManager.processRemit(order);
        }
    }

    /**
     * 出款补单
     *
     * @param remitOrderId
     */
    public void queryRemitResult(Long remitOrderId) {
        final ChannelRemitOrderEntity remitOrder = remitOrderManager.getRemitOrder(remitOrderId);
        try {
            //锁住代付订单
            final Boolean lock = redisUtils.tryGetLock(RedisLockType.REMIT_QUERY.name() + remitOrder.getId(), remitOrderId + "", 5 * 1000L);
            if (!lock) {
                log.info("出款订单:{}获取Redis锁超时", remitOrderId);
                return;
            }
            log.info("开始处理代付订单-出款补单:{}", JSONUtils.json(remitOrder));

            //校验状态必须是 出款中
            if (remitOrder.getStatus() != RemitStatusEnum.REMITTING) {
                return;
            }

            //调用di查询结果
            MybankQueryParam mybankQueryParam = new MybankQueryParam();
            mybankQueryParam.setRequestNo(remitOrder.getRequestNo());
            mybankQueryParam.setSourceDomainCode(SourceDomainEnum.LABOR.name());
            mybankQueryParam.setSourceId(remitOrder.getSupplierCorporationId().toString());
            TargetDomainEnum targetDomainEnum = TargetDomainEnum.valueOf(remitOrder.getPayChannel());
            mybankQueryParam.setTargetDomainCode(targetDomainEnum.name());
            MybankQueryResult mybankQueryResult = remitService.queryRemit(mybankQueryParam);
            log.info("queryRemitResult-提现补单查询结果:{}", JSONUtils.json(mybankQueryResult));

            //结果处理
            handleRemitQureyResult(mybankQueryResult, remitOrder);

        } finally {
            redisUtils.tryReleaseLock(RedisLockType.REMIT_QUERY.name() + remitOrder.getId(), remitOrderId + "");
        }
    }


    /**
     * 提现查询结果处理
     *
     * @param mybankQueryResult
     * @param order
     */
    public void handleRemitQureyResult(MybankQueryResult mybankQueryResult, ChannelRemitOrderEntity order) {
        //订单不存在
        if (MybankRemitStatus.NONE == mybankQueryResult.getStatus()) {
            //超过20分钟，重发
            if (order.getModifyTime().plusMinutes(30).isBefore(LocalDateTime.now())) {
                remitOrderManager.saveRemitOrder(order);
                //重发
                doRemit(order);
            }
        }

        //失败
        if (MybankRemitStatus.FAIL == mybankQueryResult.getStatus()) {
            if (order.getStatus() == RemitStatusEnum.REMIT) {
                log.info("出款订单已经【成功】，不允许处理成【失败】");
                return;
            }
            //订单失败
            proxyOrderManager.processRemitAcceptFail(order, mybankQueryResult.getErrorCode(), mybankQueryResult.getErrorMessage());
            return;
        }
        //成功
        if (MybankRemitStatus.SUCCESS == mybankQueryResult.getStatus()) {
            if (order.getStatus() == RemitStatusEnum.REMIT) {
                return;
            }
            //提现订单成功
            proxyOrderManager.processRemitSuccess(order,mybankQueryResult.getErrorCode(), mybankQueryResult.getErrorMessage());
            return;
        }
        //出款中
        if (MybankRemitStatus.REMITTING == mybankQueryResult.getStatus()) {
            return;
        }
        // 退汇
        if (MybankRemitStatus.REFUND == mybankQueryResult.getStatus()) {

            if (order.getStatus() == RemitStatusEnum.REMITTING) {
                //提现订单失败
                proxyOrderManager.processRemitAcceptFail(order, mybankQueryResult.getErrorCode(), mybankQueryResult.getErrorMessage());
                return;
            }
            if (order.getStatus() == RemitStatusEnum.REMIT) {
                //提现订单退款
                proxyOrderManager.processRefund(order, mybankQueryResult.getErrorCode(), mybankQueryResult.getErrorMessage());
            }
        }
    }
}
