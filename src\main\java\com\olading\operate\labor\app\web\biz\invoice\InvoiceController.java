package com.olading.operate.labor.app.web.biz.invoice;

import cn.hutool.core.bean.BeanUtil;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.security.AuthorityGuard;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.boot.util.validate.FileSize;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.config.NullIfEmptyInSetDeserializer;
import com.olading.operate.labor.domain.invoice.InvoiceStatus;
import com.olading.operate.labor.domain.invoice.dto.AvailableBillRequest;
import com.olading.operate.labor.domain.invoice.dto.InvoiceCreateRequest;
import com.olading.operate.labor.domain.invoice.dto.InvoicePresetRequest;
import com.olading.operate.labor.domain.invoice.dto.InvoiceReturnRequest;
import com.olading.operate.labor.domain.invoice.vo.AvailableBillVO;
import com.olading.operate.labor.domain.invoice.vo.InvoicePresetVO;
import com.olading.operate.labor.domain.invoice.vo.InvoiceVO;
import com.olading.operate.labor.domain.query.InvoiceQuery;
import com.olading.operate.labor.domain.service.InvoiceService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.info.OwnerType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 开票管理控制器
 */
@RestController
@RequestMapping("/api/supplier/invoices")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "开票管理", description = "开票申请、查询、状态管理等功能")
public class InvoiceController extends BusinessController {

    private final InvoiceService invoiceService;
    private final QueryService queryService;

    @Operation(summary = "获取开票预设信息", description = "根据合同和月份获取开票页面的预设信息，包括客户信息、发票信息、可开票明细等")
    @PostMapping("/preset-info")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_BILL)
    @AuthorityDataScopGuard(value = {
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CONTRACT, spel = "#request.contractId")
    })
    public WebApiResponse<InvoicePresetVO> getInvoicePresetInfo(@Valid @RequestBody InvoicePresetRequest request) {
        Long supplierId = currentSupplierId();
        log.info("获取开票预设信息: supplierId={}, request={}", supplierId, request);

        InvoicePresetVO presetInfo = invoiceService.getInvoicePresetInfo(supplierId, request);
        return WebApiResponse.success(presetInfo);
    }

    @Operation(summary = "获取可开票账单列表")
    @PostMapping("/available-bills")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_INVOICE)
    @AuthorityDataScopGuard(value = {
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CONTRACT, spel = "#request.contractId")
    })
    public WebApiResponse<List<AvailableBillVO>> getAvailableBills(@Valid @RequestBody AvailableBillRequest request) {
        Long supplierId = currentSupplierId();
        log.info("获取可开票账单列表: supplierId={}, request={}", supplierId, request);
        
        List<AvailableBillVO> bills = invoiceService.getAvailableBills(supplierId, request);
        return WebApiResponse.success(bills);
    }
    
    @Operation(summary = "创建开票申请")
    @PostMapping("/apply")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_INVOICE)
    @AuthorityDataScopGuard(value = {
            @AuthorityDataScopGuard.Mapping(type = OwnerType.CONTRACT, spel = "#request.contractId")
    })
    public WebApiResponse<InvoiceVO> createInvoiceApplication(@Valid @RequestBody InvoiceCreateRequest request) {
        Long supplierId = currentSupplierId();
        log.info("创建开票申请: supplierId={}, contractId={}", supplierId, request.getContractId());
        
        InvoiceVO invoice = invoiceService.createInvoiceApplication(supplierId, request);
        return WebApiResponse.success(invoice);
    }
    
    @Operation(summary = "获取开票申请详情")
    @PostMapping("/detail")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_INVOICE)
    @AuthorityDataScopGuard(return_value = {
            @AuthorityDataScopGuard.ReturnMapping(type = OwnerType.CONTRACT, spel = "data.contractId")
    })
    public WebApiResponse<InvoiceVO> getInvoiceDetail(@Valid @RequestBody InvoiceIdRequest request) {
        Long supplierId = currentSupplierId();
        log.info("获取开票申请详情: supplierId={}, invoiceId={}", supplierId, request.getInvoiceId());
        InvoiceVO invoice = invoiceService.getInvoiceDetail(supplierId, request.getInvoiceId());
        return WebApiResponse.success(invoice);
    }
    
    @Operation(summary = "分页查询开票列表")
    @PostMapping("/list")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_INVOICE)
    @AuthorityDataScopGuard(
            query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds")
    })
    public WebApiQueryResponse<InvoiceVO> queryInvoices(@RequestBody QueryFilter<WebInvoiceFilters> request) {
        Long supplierId = currentSupplierId();
        log.info("分页查询开票列表: supplierId={}, request={}", supplierId, request);
        
        QueryFilter<InvoiceQuery.Filters> filter = request.convert(WebInvoiceFilters::convert);
        filter.getFilters().setSupplierId(supplierId);
        filter.sort("id", Direction.DESCENDING);
        DataSet<InvoiceVO> ds = queryService.queryInvoice(filter);
        
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }
    
    @Operation(summary = "上传发票文件")
    @PostMapping(value = "/upload-file", consumes = {MediaType.MULTIPART_FORM_DATA_VALUE})
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_INVOICE)
    public WebApiResponse<Void> uploadInvoiceFile(
            @Valid @ModelAttribute UploadInvoiceFileRequest request) throws IOException {
        Long supplierId = currentSupplierId();
        log.info("上传发票文件: supplierId={}, invoiceId={}, fileName={}", 
                supplierId, request.getInvoiceId(), request.file.getOriginalFilename());
        // 在操作前校验权限
        invoiceService.validatePermissionWithDataScope(supplierId, request.getInvoiceId(), currentDataScope(),isAdmin());
        invoiceService.uploadInvoiceFile(supplierId, request.getInvoiceId(), request.file);
        return WebApiResponse.success();
    }
    
    @Operation(summary = "退回开票申请")
    @PostMapping("/return")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_INVOICE)
    public WebApiResponse<Void> returnInvoiceApplication(
            @Valid @RequestBody InvoiceReturnRequest request) {
        Long supplierId = currentSupplierId();
        log.info("退回开票申请: supplierId={}, invoiceId={}, reason={}", 
                supplierId, request.getInvoiceId(), request.getReason());
        // 在操作前校验权限
        invoiceService.validatePermissionWithDataScope(supplierId, request.getInvoiceId(), currentDataScope(),isAdmin());
        invoiceService.returnInvoiceApplication(supplierId, request.getInvoiceId(), request.getReason());
        return WebApiResponse.success();
    }

    @Data
    @Schema(description = "上传发票文件请求")
    public static class UploadInvoiceFileRequest {

        @NotNull(message = "账单Id不为空")
        @Schema(description = "账单ID")
        private Long invoiceId;

        @FileSize("20M")
        private MultipartFile file;
    }

    @Data
    public static class InvoiceIdRequest {
        @NotNull(message = "开票Id不能为空")
        @Schema(description = "开票Id")
        private Long invoiceId;
    }

    @Data
    @Schema(description = "查询开票")
    public static class WebInvoiceFilters {

        @Schema(description = "开票ID")
        private Long id;

        @Schema(description = "客户ID")
        @JsonDeserialize(using = NullIfEmptyInSetDeserializer.class)
        private Set<Long> customerId;

        @Schema(description = "合同ID",hidden = true)
        private Long contractId;

        @Schema(description = "申请编号")
        private String sn;

        @Schema(description = "开票状态")
        private InvoiceStatus status;

        @Schema(description = "客户名称",hidden = true)
        private String customerName;

        @Schema(description = "创建时间开始")
        private LocalDateTime createTimeStart;

        @Schema(description = "创建时间结束")
        private LocalDateTime createTimeEnd;

        @Schema(description = "合同id列表",hidden = true)
        private Set<Long> contractIds;

        public InvoiceQuery.Filters convert() {
            return BeanUtil.toBean(this, InvoiceQuery.Filters.class);
        }
    }
}