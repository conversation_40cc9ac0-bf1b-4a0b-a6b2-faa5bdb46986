package com.olading.operate.labor.domain.share.submission;

import com.olading.operate.labor.domain.TenantInfo;
import com.olading.operate.labor.domain.share.submission.vo.InfoSubmissionLaborIncomeVo;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@ActiveProfiles("test")
@Transactional
class InfoSubmissionLaborIncomeManagerTest {

    @Autowired
    private InfoSubmissionLaborIncomeManager infoSubmissionLaborIncomeManager;

    @Test
    void testAddInfoSubmissionLaborIncome() {
        // 准备测试数据
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId("test-tenant");

        InfoSubmissionLaborIncomeVo vo = new InfoSubmissionLaborIncomeVo();
        vo.setSupplierCorporationId(1L);
        vo.setSupplierId(1L);
        vo.setStartDate("2024-01-01");
        vo.setEndDate("2024-01-31");

        // 执行测试
        InfoSubmissionLaborIncomeEntity result = infoSubmissionLaborIncomeManager.addInfoSubmissionLaborIncome(tenantInfo, vo);

        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getId());
        assertEquals(vo.getSupplierCorporationId(), result.getSupplierCorporationId());
        assertEquals(vo.getSupplierId(), result.getSupplierId());
        assertEquals(vo.getStartDate(), result.getStartDate());
        assertEquals(vo.getEndDate(), result.getEndDate());
    }

    @Test
    void testQueryInfoSubmissionLaborIncome() {
        // 准备测试数据
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId("test-tenant");

        InfoSubmissionLaborIncomeVo vo = new InfoSubmissionLaborIncomeVo();
        vo.setSupplierCorporationId(1L);
        vo.setSupplierId(1L);
        vo.setStartDate("2024-01-01");
        vo.setEndDate("2024-01-31");

        // 先添加记录
        InfoSubmissionLaborIncomeEntity entity = infoSubmissionLaborIncomeManager.addInfoSubmissionLaborIncome(tenantInfo, vo);

        // 查询记录
        InfoSubmissionLaborIncomeVo result = infoSubmissionLaborIncomeManager.queryInfoSubmissionLaborIncome(entity.getId());

        // 验证结果
        assertNotNull(result);
        assertEquals(entity.getId(), result.getId());
        assertEquals(vo.getSupplierCorporationId(), result.getSupplierCorporationId());
        assertEquals(vo.getSupplierId(), result.getSupplierId());
        assertEquals(vo.getStartDate(), result.getStartDate());
        assertEquals(vo.getEndDate(), result.getEndDate());
    }

    @Test
    void testUpdateInfoSubmissionLaborIncome() {
        // 准备测试数据
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId("test-tenant");

        InfoSubmissionLaborIncomeVo vo = new InfoSubmissionLaborIncomeVo();
        vo.setSupplierCorporationId(1L);
        vo.setSupplierId(1L);
        vo.setStartDate("2024-01-01");
        vo.setEndDate("2024-01-31");

        // 先添加记录
        InfoSubmissionLaborIncomeEntity entity = infoSubmissionLaborIncomeManager.addInfoSubmissionLaborIncome(tenantInfo, vo);

        // 更新记录
        InfoSubmissionLaborIncomeVo updateVo = new InfoSubmissionLaborIncomeVo();
        updateVo.setId(entity.getId());
        updateVo.setStartDate("2024-02-01");
        updateVo.setEndDate("2024-02-28");

        InfoSubmissionLaborIncomeEntity result = infoSubmissionLaborIncomeManager.updateInfoSubmissionLaborIncome(updateVo);

        // 验证结果
        assertNotNull(result);
        assertEquals(entity.getId(), result.getId());
        assertEquals("2024-02-01", result.getStartDate());
        assertEquals("2024-02-28", result.getEndDate());
    }

    @Test
    void testDeleteInfoSubmissionLaborIncome() {
        // 准备测试数据
        TenantInfo tenantInfo = new TenantInfo();
        tenantInfo.setTenantId("test-tenant");

        InfoSubmissionLaborIncomeVo vo = new InfoSubmissionLaborIncomeVo();
        vo.setSupplierCorporationId(1L);
        vo.setSupplierId(1L);
        vo.setStartDate("2024-01-01");
        vo.setEndDate("2024-01-31");

        // 先添加记录
        InfoSubmissionLaborIncomeEntity entity = infoSubmissionLaborIncomeManager.addInfoSubmissionLaborIncome(tenantInfo, vo);
        Long entityId = entity.getId();

        // 删除记录
        infoSubmissionLaborIncomeManager.deleteInfoSubmissionLaborIncome(entityId);

        // 验证记录已删除
        assertThrows(Exception.class, () -> {
            infoSubmissionLaborIncomeManager.queryInfoSubmissionLaborIncome(entityId);
        });
    }
}
