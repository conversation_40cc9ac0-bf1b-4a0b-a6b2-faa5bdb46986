package com.olading.operate.labor.app.web.biz.proxy;

import cn.hutool.core.collection.CollectionUtil;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.olading.boot.core.business.webapi.WebApiQueryResponse;
import com.olading.boot.core.business.webapi.WebApiResponse;
import com.olading.boot.core.security.AuthorityGuard;
import com.olading.boot.util.DataSet;
import com.olading.boot.util.jpa.querydsl.Direction;
import com.olading.boot.util.jpa.querydsl.QueryFilter;
import com.olading.operate.labor.app.Authority;
import com.olading.operate.labor.app.aspect.AuthorityDataScopGuard;
import com.olading.operate.labor.app.web.biz.BusinessController;
import com.olading.operate.labor.domain.proxy.ProxyOrderBatchDetailData;
import com.olading.operate.labor.domain.proxy.ProxyOrderData;
import com.olading.operate.labor.domain.proxy.ProxyOrderManager;
import com.olading.operate.labor.domain.proxy.order.ProxyBatchStatusEnum;
import com.olading.operate.labor.domain.proxy.order.ProxyOrderStatusEnum;
import com.olading.operate.labor.domain.query.BusinessContractQuery;
import com.olading.operate.labor.domain.query.ProxyBatchQuery;
import com.olading.operate.labor.domain.query.ProxyOrderQuery;
import com.olading.operate.labor.domain.query.SalaryQuery;
import com.olading.operate.labor.domain.salary.vo.SalaryVO;
import com.olading.operate.labor.domain.service.ProxyOrderService;
import com.olading.operate.labor.domain.service.QueryService;
import com.olading.operate.labor.domain.share.contract.vo.ContractVo;
import com.olading.operate.labor.domain.share.info.OwnerType;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

@Tag(name = "代发接口")
@RestController
@RequestMapping("/api/supplier")
@RequiredArgsConstructor
@Slf4j
public class ProxyController extends BusinessController {

    private final ProxyOrderManager proxyOrderManager;
    private final QueryService queryService;
    private final ProxyOrderService proxyOrderService;

    @Operation(summary = "查询代发批次")
    @PostMapping(value = "listProxyBatch")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_BATCH)
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds"),
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CORPORATION, spel = "#request.filters.corporationIds")
    })
    public WebApiQueryResponse<ProxyOrderBatchDetailData> listProxyBatch(@RequestBody QueryFilter<ListProxyBatchFilters> request) {
        QueryFilter<ProxyBatchQuery.Filters> filter = request.convert(ListProxyBatchFilters::convert);
        filter.getFilters().setSupplierId(currentSupplierId());
        filter.sort("id", Direction.DESCENDING);
        DataSet<ProxyOrderBatchDetailData> ds = queryService.queryProxyOrderBatch(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @Operation(summary = "查询代发列表")
    @PostMapping(value = "listProxy")
    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_ORDER)
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.filters.contractIds"),
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CORPORATION, spel = "#request.filters.corporationIds")
    })
    public WebApiQueryResponse<ProxyOrderData> listProxy(@RequestBody QueryFilter<ListProxyFilters> request) {
        QueryFilter<ProxyOrderQuery.Filters> filter = request.convert(ListProxyFilters::convert);
        filter.getFilters().setSupplierId(currentSupplierId());
        filter.sort("id", Direction.DESCENDING);
        DataSet<ProxyOrderData> ds = queryService.queryProxyOrder(filter);
        return WebApiQueryResponse.success(ds.getData(), ds.getTotal());
    }

    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_BATCH)
    @Operation(summary = "查询批次汇总信息")
    @PostMapping("batchSummary")
    @AuthorityDataScopGuard(return_value = {
            @AuthorityDataScopGuard.ReturnMapping(type = OwnerType.CONTRACT, spel = "data.contractId")
    })
    public WebApiResponse<ProxyOrderBatchDetailData> batchSummary(@Valid @RequestBody IdRequest request) {

        ProxyOrderBatchDetailData orderBatchDetailData = proxyOrderManager.getOrderBatchDetailData(request.getId());
        return WebApiResponse.success(orderBatchDetailData);
    }

    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_BATCH)
    @Operation(summary = "删除批次")
    @PostMapping("deleteBatch")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.contractIds")
    })
    public WebApiResponse<Void> deleteBatch(@Valid @RequestBody IdRequest request) {
        QueryFilter<ProxyBatchQuery.Filters> queryFilter = new QueryFilter<>();
        queryFilter.setFilters(ProxyBatchQuery.Filters.builder().id(request.getId()).contractIds(request.getContractIds()).build());
        DataSet<ProxyOrderBatchDetailData> dataSet = queryService.queryProxyOrderBatch(queryFilter);
        if(CollectionUtil.isEmpty(dataSet.getData())){
            throw new SecurityException("无权限操作");
        }
        proxyOrderService.removeBatch(request.getId());
        return WebApiResponse.success();
    }


    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_BATCH)
    @Operation(summary = "确认支付")
    @PostMapping("confirmPay")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.contractIds")
    })
    public WebApiResponse<Void> confirmPay(@Valid @RequestBody IdRequest request) {
        QueryFilter<ProxyBatchQuery.Filters> queryFilter = new QueryFilter<>();
        queryFilter.setFilters(ProxyBatchQuery.Filters.builder().id(request.getId()).contractIds(request.getContractIds()).build());
        DataSet<ProxyOrderBatchDetailData> dataSet = queryService.queryProxyOrderBatch(queryFilter);
        if(CollectionUtil.isEmpty(dataSet.getData())){
            throw new SecurityException("无权限操作");
        }
        proxyOrderService.confirmBatch(request.getId());
        return WebApiResponse.success();
    }

    @AuthorityGuard(any = Authority.SUPPLIER_STATEMENT_SALARY_BATCH)
    @Operation(summary = "生成代付批次")
    @PostMapping("createBatch")
    @AuthorityDataScopGuard(query_value = {
            @AuthorityDataScopGuard.QueryMapping(type = OwnerType.CONTRACT, spel = "#request.contractIds")
    })
    public WebApiResponse<Long> confirmPay(@Valid @RequestBody CreateBatchRequest request) {
        final SalaryQuery.Filters filters = new SalaryQuery.Filters();
        filters.setId(request.getId());
        filters.setContractIds(request.getContractIds());
        QueryFilter<SalaryQuery.Filters> filtersQueryFilter = new QueryFilter<>(filters);
        final DataSet<SalaryVO> dataSet = queryService.querySalary(filtersQueryFilter);
        if(CollectionUtil.isEmpty(dataSet.getData())){
            throw new SecurityException("无权限操作");
        }
        final Long batch = proxyOrderService.createBatch(request.getId(), request.getRemark());
        return WebApiResponse.success(batch);
    }




    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    @Schema(description = "代发批次查询条件")
    public static class ListProxyBatchFilters {

        @Schema(description = "代发批次ID")
        private Long id;

        @Schema(description = "作业主体名称")
        private String corporation;

        @Schema(description = "合同名称")
        private String businessContract;

        @Schema(description = "客户名称")
        private String customer;

        @Schema(description = "作业主体ID列表")
        private Set<Long> corporationIds;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;

        @Schema(description = "批次状态")
        private ProxyBatchStatusEnum batchStatus;

        public ProxyBatchQuery.Filters convert() {
            return ProxyBatchQuery.Filters.builder()
                    .id(this.id)
                    .corporation(this.corporation)
                    .businessContract(this.businessContract)
                    .customer(this.customer)
                    .corporationIds(this.corporationIds)
                    .contractIds(this.contractIds)
                    .batchStatus(this.batchStatus)
                    .build();
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    @Schema(description = "代发订单查询条件")
    public static class ListProxyFilters {

        @Schema(description = "代发订单Id")
        private Long id;

        @Schema(description = "作业主体名称")
        private String corporation;

        @Schema(description = "合同名称")
        private String businessContract;

        @Schema(description = "代发批次Id")
        private Long batchId;

        @Schema(description = "工资明细Id")
        private Long salaryDetailId;

        @Schema(description = "作业主体Id列表")
        private Set<Long> corporationIds;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;

        @Schema(description = "代发状态")
        private ProxyOrderStatusEnum status;

        public ProxyOrderQuery.Filters convert() {
            return ProxyOrderQuery.Filters.builder()
                    .id(this.id)
                    .batchId(this.batchId)
                    .salaryDetailId(this.salaryDetailId)
                    .corporation(this.corporation)
                    .contractIds(this.contractIds)
                    .status(this.status)
                    .businessContract(this.businessContract)
                    .corporationIds(this.corporationIds)
                    .batchId(this.batchId)
                    .build();
        }
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class IdRequest{
        @NotNull(message = "批次ID不能为空")
        @Schema(description = "批次ID")
        private Long id;
        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;
    }

    @JsonInclude(JsonInclude.Include.NON_NULL)
    @Data
    public static class CreateBatchRequest{
        @NotNull(message = "工资表id不能为空")
        @Schema(description = "工资表id")
        private Long id;

        private String remark;

        @Schema(description = "合同ID列表", hidden = true)
        private Set<Long> contractIds;

    }



}
